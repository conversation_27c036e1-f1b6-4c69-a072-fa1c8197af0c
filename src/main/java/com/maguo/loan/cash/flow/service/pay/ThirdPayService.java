package com.maguo.loan.cash.flow.service.pay;



import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/19
 */
@Service
public class ThirdPayService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPayService.class);

    private WithholdFlowRepository withholdFlowRepository;


    @Autowired
    private BaofuPayService baofuPayService;

    private IPayService getPayService(ChargeBizType chargeBizType) {
        return baofuPayService;
    }


    public WithholdFlow apply(String chargeId) {
        //根据扣款记录id获取扣款记录
        WithholdFlow withholdFlow = withholdFlowRepository.findById(chargeId).orElseThrow();
        // 申请代扣
        return getPayService(withholdFlow.getBizType()).payApply(withholdFlow);
    }

    public void apply(WithholdFlow withholdFlow) {
        getPayService(withholdFlow.getBizType()).payApply(withholdFlow);
    }

    public void query(String chargeId) {
        WithholdFlow withholdFlow = withholdFlowRepository.findById(chargeId).orElseThrow();
        if (withholdFlow.getPayState().isFinal()) {
            logger.error("扣款结果已是终态, 跳过查询:{}", chargeId);
            return;
        }
        // 查询
        getPayService(withholdFlow.getBizType()).payResult(withholdFlow);
    }

    @Autowired
    public void setWithholdFlowRepository(WithholdFlowRepository withholdFlowRepository) {
        this.withholdFlowRepository = withholdFlowRepository;
    }
}
