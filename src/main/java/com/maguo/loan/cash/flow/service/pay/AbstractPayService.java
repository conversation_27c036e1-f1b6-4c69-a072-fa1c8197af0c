package com.maguo.loan.cash.flow.service.pay;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.WithholdAccountConfigRepository;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import com.maguo.loan.cash.flow.repository.WithholdShareInfoRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.event.PrincipalRepaySucceedEvent;

import com.zsjz.third.part.Status;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeQueryResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-01-07
 */
public abstract class AbstractPayService implements IPayService {

    @Autowired
    protected CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    protected WithholdFlowRepository withholdFlowRepository;

    @Autowired
    protected MqService mqService;

    @Autowired
    protected WarningService warningService;

    @Autowired
    protected ApplicationEventPublisher eventPublisher;

    @Autowired
    protected SmsService smsService;

    @Autowired
    protected LoanRepository loanRepository;

    @Autowired
    protected UserInfoRepository userInfoRepository;

    @Autowired
    protected RepayPlanRepository repayPlanRepository;

    @Autowired
    protected UserBankCardRepository userBankCardRepository;

    @Autowired
    protected WithholdShareInfoRepository withholdShareInfoRepository;

    @Autowired
    protected WithholdAccountConfigRepository withholdAccountConfigRepository;

    protected static final String DEFAULT_ORDER_PREFIX_NO = "QY01DF";

    /**
     * 处理公共支付结果
     *
     * @param withholdFlow      withholdFlow
     * @param chargeQueryResult chargeQueryResult
     */
    protected void handlePayResult(WithholdFlow withholdFlow, BaoFuChargeQueryResponse chargeQueryResult) {
        switch (withholdFlow.getBizType()) {

            case FINANCE -> financeResult(withholdFlow, chargeQueryResult);
            default -> {
            }
        }
    }


//    /**
//     * 融担扣费
//     */
//    private void guaranteeResult(WithholdFlow withholdFlow, ChargeQueryResult chargeQueryResult) {
//        updateWithholdFlow(withholdFlow, chargeQueryResult);
//        eventPublisher.publishEvent(new FeeRepayResultEvent(withholdFlow.getRepayRecordId(), withholdFlow.getPayState(),
//            chargeQueryResult.getRespMsg()));
//    }

    /**
     * 本息（）扣费结果
     */
    private void financeResult(WithholdFlow withholdFlow, BaoFuChargeQueryResponse chargeQueryResult) {
        // 扣款记录
        updateWithholdFlow(withholdFlow, chargeQueryResult);
    }


    /**
     * 更新代扣记录
     */
    private void updateWithholdFlow(WithholdFlow withholdFlow, BaoFuChargeQueryResponse chargeQueryResult) {
        // 代扣结果处理
        if (Objects.equals(chargeQueryResult.getOrderStatus(), Status.SUCCESS)) {
            withholdFlow.setPayState(ProcessState.SUCCEED);
            withholdFlow.setPayOrderNo(chargeQueryResult.getPayOrderId());
            withholdFlow.setPayTime(LocalDateTime.now());
        } else if (Objects.equals(chargeQueryResult.getOrderStatus(), Status.FAIL)) {
            withholdFlow.setPayState(ProcessState.FAILED);
            withholdFlow.setFailReason(chargeQueryResult.getMsg());
        }
        withholdFlowRepository.save(withholdFlow);
    }

    /**
     * 更新对客还款记录
     */
    private void updateRecordForFinance(WithholdFlow withholdFlow) {
        CustomRepayRecord repayRecord = customRepayRecordRepository.findById(withholdFlow.getRepayRecordId())
                .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        if (ProcessState.SUCCEED == withholdFlow.getPayState()) {
            repayRecord.setRepaidDate(withholdFlow.getPayTime());
            customRepayRecordRepository.save(repayRecord);
            // 后续费用处理
            eventPublisher.publishEvent(new PrincipalRepaySucceedEvent(withholdFlow.getRepayRecordId()));
        } else if (ProcessState.FAILED == withholdFlow.getPayState()) {
            repayRecord.setRepayState(ProcessState.FAILED);
            repayRecord.setFailReason(withholdFlow.getFailReason());
            customRepayRecordRepository.save(repayRecord);
        }
    }


    /**
     * 更新还款计划
     */
    private void updatePlanForFinance(WithholdFlow withholdFlow) {
        CustomRepayRecord repayRecord =
                customRepayRecordRepository.findById(withholdFlow.getRepayRecordId()).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        Loan loan = loanRepository.findById(repayRecord.getLoanId()).orElseThrow();

        String loanId = loan.getId();
        Integer period = repayRecord.getPeriod();
        List<RepayPlan> planList = repayPlanRepository.findByLoanIdOrderByPeriod(loanId);
        final int curPeriod = period;
        RepayPurpose purpose = repayRecord.getRepayPurpose();
        RepayPlan curPlan =
                planList.stream().filter(p -> p.getPeriod().compareTo(curPeriod) == 0).findFirst().orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        curPlan.setActAmount(withholdFlow.getPayAmount());
        curPlan.setActPrincipalAmt(repayRecord.getPrincipalAmt());
        curPlan.setActInterestAmt(repayRecord.getInterestAmt());
        curPlan.setActPenaltyAmt(repayRecord.getCapitalPenaltyAmt());
        curPlan.setActCapitalPenaltyAmt(repayRecord.getCapitalPenaltyAmt());
        curPlan.setActGuaranteeAmt(repayRecord.getCapitalGuaranteeAmt());


        curPlan.setActPenaltyAmt(repayRecord.getPenaltyAmt());
        curPlan.setActGuaranteeAmt(repayRecord.getGuaranteeAmt());
        curPlan.setActConsultFee(repayRecord.getConsultFee());


        curPlan.setActBreachAmt(repayRecord.getBreachAmt());
        curPlan.setActRepayTime(withholdFlow.getPayTime());
        repayPlanRepository.save(curPlan);
        if (RepayPurpose.CLEAR == purpose) {
            planList.stream().filter(p -> p.getPeriod() > curPeriod).forEach(p -> {
                p.setActAmount(BigDecimal.ZERO);
                p.setActPrincipalAmt(BigDecimal.ZERO);
                p.setActInterestAmt(BigDecimal.ZERO);
                p.setActPenaltyAmt(BigDecimal.ZERO);
                p.setActCapitalPenaltyAmt(BigDecimal.ZERO);
                p.setActGuaranteeAmt(BigDecimal.ZERO);
                if (Payee.GUARANTEE == withholdFlow.getPayee()) {
                    p.setActConsultFee(BigDecimal.ZERO);
                }
                p.setActBreachAmt(BigDecimal.ZERO);
                p.setActRepayTime(withholdFlow.getPayTime());
                repayPlanRepository.save(p);
            });
        }
    }

    public static String generateOrderPreNo(ChargeBizType bizType, Loan loan) {
        BankChannel bankChannel = loan.getBankChannel();
        String channelCode = convertBankChannel(bankChannel);
        String bizTypeCode = convertChargeBizType(bizType);
        String orderNo = channelCode + bizTypeCode;
        if (StringUtil.isEmpty(orderNo)) {
            return DEFAULT_ORDER_PREFIX_NO;
        }
        return orderNo;
    }


    /**
     * 转换资方渠道和扣款单号的关系（BRP-7554)
     * 新接渠道需要修改代码
     *
     * @param channel 渠道
     * @return 单号编码
     */
    private static String convertBankChannel(BankChannel channel) {
        return switch (channel) {

            case CYBK -> "CYBK";
            default -> "";
        };
    }

    private static String convertChargeBizType(ChargeBizType bizType) {
        return switch (bizType) {
            case RIGHTS -> "";
            case FINANCE -> "HK";
            case EXT_GUARANTEE -> "DB";
            case CONSULT -> "ZX";
            case GUARANTEE_CONSULT -> "DZ";
        };
    }
}
